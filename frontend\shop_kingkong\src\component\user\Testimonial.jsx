import React from "react";

const Testimonial = () => {
  const testimonials = [
    {
      id: 1,
      image: "https://via.placeholder.com/150",
      content:
        "Túi của bagShop không chỉ là phụ kiện mà còn là tuyên ngôn cá tính. Chất lượng vượt trội, thiết kế sáng tạo đích thực là điểm nhấn của tôi!",
      name: "<PERSON>ễ<PERSON>",
      position: "Nhà thiết kế",
    },
    {
      id: 2,
      image: "https://via.placeholder.com/150",
      content:
        "Túi của bagShop không chỉ là phụ kiện mà còn là tuyên ngôn cá tính. Chất lượng vượt trội, thiết kế sáng tạo đích thực là điểm nhấn của tôi!",
      name: "<PERSON>",
      position: "<PERSON><PERSON><PERSON> thiết kế",
    },
    {
      id: 3,
      image: "https://via.placeholder.com/150",
      content:
        "Túi của bagShop không chỉ là phụ kiện mà còn là tuyên ngôn cá tính. Chất lượng vượt trội, thiết kế sáng tạo đích thực là điểm nhấn của tôi!",
      name: "Minh Nguyễn",
      position: "Nhà thiết kế",
    },
  ];

  return (
    <div className="container px-20 py-24">
      <div className="flex items-start justify-between gap-16">
        <div className="flex-1">
          <div className="text-5xl md:text-5xl lg:text-6xl text-black-700">
            Khách Hàng Nói Gì Về Chúng Tôi
          </div>
        </div>
        <div className="flex-1 space-y-6">
          {testimonials.map((testimonial, index) => (
            <p key={index} className="text-lg text-gray-700 leading-relaxed">
              {testimonial.content}
              <span className="font-bold">{testimonial.name}</span>
              <span>{testimonial.position}</span>
            </p>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Testimonial;
